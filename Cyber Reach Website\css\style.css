body {
  margin: 0;
  font-family: 'Poppins', sans-serif;
  background-color: black;
}


.navbar {
  position: fixed; /* Keep the position fixed */
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000; /* Ensure navbar is above other content */
  background-color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.nav-list.active {
  display: flex;
  flex-direction: column;
  position: fixed;
  top: 75px;
  left: -5px;
  width: 100%;
  background-color: #1D3670;
  border-bottom-left-radius: 20px;
  border-bottom-right-radius: 20px;
  margin-top: 20px;
  z-index: 1;
  padding-left: 2px;
}



.logo-container img {
  height: 60px; /* Increased logo size to 60px */
  margin-right: 10px;
}


.menu-icon {
  font-size: 24px;
  color: black;
  cursor: pointer;
  display: none;
}


.nav-list {
  list-style-type: none;
  display: flex;
  align-items: center;
}

.nav-list li {
  margin-right: 20px;
}

.nav-list a {
  text-decoration: none;
  color: black;
  transition: color 0.3s ease-in-out;
}

.nav-list a:hover {
  color: #FFD700; /* Gold/Yellow */
}



@media only screen and (max-width: 768px) {
  .nav-list {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    top: 60px;
    left: 0;
    width: 100%;
    background-color: #1D3670;
    border-bottom-left-radius: 20px;
    border-bottom-right-radius: 20px;
    margin: 3px; /* Adjust the margin as needed */
    z-index: 1;
  }



  .menu-icon {
    display: block;
  }

}

.main-heading-slide {
  position: relative;
  height: 100vh;
  overflow: hidden;
  color: #FFFFFF; /* Text color */
}

.background-image {
  background-image: url('../images/White\ Aesthetic\ Inspirational\ Desktop\ Wallpaper\ .png');
  opacity: 1; /* Replace with your image URL */
  background-size: cover;
  background-position: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.main-heading-slide {
  position: relative;
  height: 100vh;
  overflow: hidden;
  color:  #3355A2; /* Text color */
}

.main-heading-slide h1.glowing-text {
  font-size: 7rem; /* Adjust the font size as needed */
  font-weight: bold;
  margin-bottom: 10px;
  animation: glowAnimation 5s infinite;
}

.main-heading-slide h1.glowing-text span {
  font-size: 9rem;
}


@media only screen and (max-width: 768px) {
  .glowing-text {
    font-size: 2rem;
  }
}

  @media only screen and (max-width: 1200px) {
    .main-heading-slide h1.glowing-text {
      font-size: 4rem; /* Adjust the font size for medium-sized screens */
    }
    .main-heading-slide h1.glowing-text span {
      font-size: 6rem; /* Adjust the span font size for medium-sized screens */
    }
  }

  @media only screen and (max-width: 768px) {
    .main-heading-slide h1.glowing-text {
      font-size: 3rem; /* Adjust the font size for smaller screens */
    }
    .main-heading-slide h1.glowing-text span {
      font-size: 5rem; /* Adjust the span font size for smaller screens */
    }
  }

  @keyframes glowAnimation {
    0% {
      text-shadow: 0 0 2px #3355A2, 0 0 5px #3355A2, 0 0 8px #3355A2;
    }
    50% {
      text-shadow: 0 0 2px #1D3670, 0 0 5px #1D3670, 0 0 8px #1D3670;
    }
    100% {
      text-shadow: 0 0 2px #3355A2, 0 0 5px #3355A2, 0 0 8px #3355A2;
    }
  }

  .cyber-calendar-slide {
    position: relative;
    height: 50vh; /* Adjust the height as needed */
    overflow: hidden;
    color: white;
  }

  .background-video {
    object-fit: cover;
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -1;
  }

  .cyber-calendar-slide .content {
    text-align: center;
    z-index: 2;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 80%; /* Adjust the width as needed */
    padding: 20px;
    box-sizing: border-box;
  }

  .cyber-calendar-slide h2 {
    font-size: 4rem;
    margin-bottom: 10px;
    padding-top: 20px;
  }

  .cyber-calendar-slide p {
    font-size: 1.2rem;
    margin-bottom: 20px;
  }

  .cyber-calendar-slide .btn {
    display: inline-block;
    padding: 12px 24px;
    font-size: 1.2rem;
    text-decoration: none;
    color: black;
    background-color: #FF6680;
    border-radius: 5px;
    transition: background-color 0.3s ease-in-out;
  }

  .cyber-calendar-slide .btn:hover {
    background-color: #FF3355;
  }

  @media only screen and (max-width: 768px) {
    .cyber-calendar-slide .content {
      width: 50%; /* Adjust the width for smaller screens */
    }
  }










  .animated h2,
.animated p,
.animated .btn {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 1s ease, transform 1s ease;
}

/* Apply styles when the section is in view */
.animated.in-view h2,
.animated.in-view p,
.animated.in-view .btn {
  opacity: 1;
  transform: translateY(0);
}
.animated h2,
.animated p,
.animated .btn {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 1s ease, transform 1s ease;
}

/* Apply styles when the section is in view */
.animated.in-view h2,
.animated.in-view p,
.animated.in-view .btn {
  opacity: 1;
  transform: translateY(0);
}

@media only screen and (max-width: 320px) {
  .main-heading-slide h1.glowing-text {
    font-size: 2rem;
  }

  .main-heading-slide h1.glowing-text span {
    font-size: 3rem;
  }


  .cyber-calendar-slide h2 {
    font-size: 1.5rem;
  }

  .cyber-calendar-slide p {
    font-size: 0.9rem;
  }

  .cyber-calendar-slide .content {
    width: 100%; /* Adjust width for 320px screens */
    padding: 10px; /* Adjust padding for 320px screens */
  }


}

@media only screen and (max-width: 375px) {
  .main-heading-slide h1.glowing-text {
    font-size: 3rem;
  }

  .main-heading-slide h1.glowing-text span {
    font-size: 4rem;
  }


  .cyber-calendar-slide h2 {
    font-size: 1.8rem;
  }

  .cyber-calendar-slide p {
    font-size: 1rem;
  }


}

@media only screen and (max-width: 480px) {
  .main-heading-slide h1.glowing-text {
    font-size: 2rem;
  }

  .main-heading-slide h1.glowing-text span {
    font-size: 5rem;
  }



  .cyber-calendar-slide h2 {
    font-size: 2.2rem;
  }

  .cyber-calendar-slide p {
    font-size: 1.2rem;
  }

}

.scam-diaries {
  position: relative;
  overflow: hidden;
  height: 50vh; /* Adjust the height as needed */
}

.scam-diaries {
  position: relative;
  overflow: hidden;
  height: 50vh; /* Adjust the height as needed */
}

.scam-diaries .background-image {
  background-image: url('../images/Untitled\ design\ \(3\).jpg'); /* Add your background image path */
  background-size: cover;
  background-position: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.scam-diaries .content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white; /* Adjust the text color */
  z-index: 1;
  text-align: center;
}

.scam-diaries h2 {
  font-size: 4rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: white; /* Change the text color to a contrasting one */
}

.scam-diaries p {
  font-size: 1.2rem;
  margin-bottom: 20px;
  color: white /* Change the text color to a contrasting one */
}

.scam-diaries .btn {
  display: inline-block;
  padding: 15px 30px;
  font-size: 1.2rem;
  text-decoration: none;
  color: #FFF;
  background-color: #39408C;
  border-radius: 5px;
  transition: background-color 0.3s ease-in-out;
  margin-top: 20px; /* Adjust spacing if needed */
}

.scam-diaries .btn:hover {
  background-color: #140F47;
}

@media only screen and (max-width: 768px) {
  .scam-diaries .content {
    width: 50%; /* Adjust the width for smaller screens */
  }

  .scam-diaries h2 {
    font-size: 1.5rem;
  }

  .scam-diaries p {
    font-size: 1rem;
  }

  .scam-diaries .btn {
    font-size: 0.8rem;
  }
}

@media only screen and (max-width: 768px) {
  .cyber-calendar-slide .btn {
    padding: 10px 20px;
    font-size: 1rem;
  }
}

@media only screen and (max-width: 480px) {
  .cyber-calendar-slide .btn {
    padding: 6px 12px;
    font-size: 0.8rem;
  }
}

@media only screen and (max-width: 320px) {
  .cyber-calendar-slide .btn {
    font-size: 0.9rem; /* Adjust font size for 320px screens */
    padding: 10px 20px; /* Adjust padding for 320px screens */
  }

}

.blogs {
  position: relative;
  height: 50vh;
  overflow: hidden;
  color: white;
}

.background-video {
  object-fit: cover;
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: -1;
}

.blogs .content {
  text-align: center;
  z-index: 2;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  padding: 20px;
  box-sizing: border-box;
}

.blogs h2 {
  font-size: 4rem;
  margin-bottom: 10px;
}

.blogs p {
  font-size: 1.2rem;
  margin-bottom: 20px;
}

.blogs .btn {
  display: inline-block;
  padding: 12px 24px;
  font-size: 1.2rem;
  text-decoration: none;
  color: white;
  background-color: #452180;
  border-radius: 5px;
  transition: background-color 0.3s ease-in-out;
}

.blogs .btn:hover {
  background-color: #100432;
}

@media only screen and (max-width: 768px) {
  .blogs .content {
    width: 80%; /* Adjust the width for smaller screens */
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .blogs h2 {
    font-size: 3rem;
  }

  .blogs p {
    font-size: 1rem;
  }

  .blogs .btn {
    font-size: 1rem; /* Adjust font size for smaller screens */
  }
}

@media only screen and (max-width: 480px) {
  .blogs .content {
    width: 90%; /* Further adjust the width for even smaller screens */
  }

  .blogs h2 {
    font-size: 2rem;
  }

  .blogs p {
    font-size: 0.8rem;
  }

  .blogs .btn {
    font-size: 0.8rem;
  }
}

/* Footer Styles */

footer {
  background-color: #1E90FF;
  color: white;
  padding: 30px 0;
  transition: opacity 1s ease, transform 1s ease;
}

.footer-content {
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}

/* Follow Us Section Styles */
.follow-us,
.contact-us {
  flex: 1;
  margin: 0 20px;
}

.follow-us h3,
.contact-us h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
}

.social-icons {
  display: flex;
  gap: 10px;
}

.social-icons img {
  width: 30px; /* Adjust the size as needed */
  height: 30px;
}

/* Contact Us Section Styles */
.contact-us .email-icon {
  display: flex;
  align-items: center;
}

.email-icon img {
  width: 25px; /* Adjust the size as needed */
  height: 25px;
  margin-right: 10px;
}


.email-icon a {
  color: white;
  text-decoration: none;
}

/* Copyright Section Styles */
.copyright {
  text-align: center;
  margin-top: 20px;
}

/* Responsive Styles */
@media only screen and (max-width: 768px) {
  .footer-content {
    flex-direction: column;
    align-items: center;
  }

  .follow-us,
  .contact-us {
    margin: 10px 0; /* Adjust margin for responsiveness */
    text-align: center;
  }
}

.animated.in-view {
  opacity: 1;
  transform: translateY(0);
}

.about-cybereach {
  position: relative;
  overflow: hidden;
  height: 50vh; /* Adjust the height as needed */
}

.about-cybereach .background-image {
  background-image: url('../images/0\ 1\ 0\ 1\ 0\ 1\ 0\ 1\ 0\ 1\ 0\ 1\ 0\ 1\ 0\ 1\ 0\ 1\ 0\ \(1\).png');
  opacity: 0.7;/* Add the path to your background image */
  background-size: cover;
  background-position: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
}

.about-cybereach .content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white; /* Adjust the text color */
  z-index: 1;
  text-align: center;
}

.about-cybereach h2 {
  font-size: 4rem;
  font-weight: bold;
  margin-bottom: 10px;
  color: white; /* Change the text color to a contrasting one */
}

.about-cybereach p {
  font-size: 1.2rem;
  margin-bottom: 20px;
  color: white; /* Change the text color to a contrasting one */
}

.about-cybereach .btn {
  display: inline-block;
  padding: 15px 30px;
  font-size: 1.2rem;
  text-decoration: none;
  color: black;
  background-color: #66ff66;
  border-radius: 5px;
  transition: background-color 0.3s ease-in-out;
  margin-top: 20px; /* Adjust spacing if needed */
}

.about-cybereach .btn:hover {
  background-color: #009c1a;
}

@media only screen and (max-width: 768px) {
  .about-cybereach .content {
    width: 50%; /* Adjust the width for smaller screens */
  }

  .about-cybereach h2 {
    font-size: 3rem;
  }

  .about-cybereach p {
    font-size: 1rem;
  }

  .about-cybereach .btn {
    font-size: 0.8rem;
    margin-bottom: 40px
  }
}

@media only screen and (max-width: 425px) {
  .about-cybereach .btn {
    padding: 12px 24px; /* Adjust padding for smaller screens */
    font-size: 0.9rem; /* Adjust font size for smaller screens */
    display: block;
    margin-bottom: 85px; /* Center the button */
  }
  }

@media only screen and (max-width: 768px){
.cyber-calendar-slide .btn {
  margin-bottom: 80px;
}
}

/* Button Group */
.button-group {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}

/* News Peek Button */
.news-peek-btn {
  background: linear-gradient(45deg, #FF6680, #ff4757);
  color: white;
  border: none;
  padding: 12px 25px;
  font-family: 'Poppins', sans-serif;
  font-weight: 600;
  font-size: 0.9rem;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: none;
  letter-spacing: 0.5px;
  box-shadow: 0 4px 15px rgba(255, 102, 128, 0.3);
  display: flex;
  align-items: center;
  gap: 8px;
  position: relative;
  overflow: hidden;
}

.news-peek-btn:hover {
  background: linear-gradient(45deg, #ff4757, #FF6680);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(255, 102, 128, 0.5);
}

.news-peek-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.news-peek-btn:hover::before {
  left: 100%;
}

.news-icon {
  font-size: 1.1rem;
  animation: newsIconBounce 2s ease-in-out infinite;
}

@keyframes newsIconBounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-3px); }
}

@media (max-width: 768px) {
  .button-group {
    gap: 12px;
  }

  .news-peek-btn {
    font-size: 0.8rem;
    padding: 10px 20px;
  }
}

/* Enhanced UI Styles and Newspaper Section */

/* Enhanced Cyber News Section */
.cyber-news-section {
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  padding: 80px 20px;
  position: relative;
  overflow: hidden;
  border-top: 3px solid #FF6680;
  border-bottom: 3px solid #3355A2;
}

.cyber-news-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    radial-gradient(circle at 20% 50%, rgba(51, 85, 162, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 102, 128, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 80%, rgba(29, 54, 112, 0.15) 0%, transparent 50%);
  pointer-events: none;
  animation: cyberPulse 4s ease-in-out infinite;
}

@keyframes cyberPulse {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.6; }
}

.newspaper-container {
  max-width: 1200px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
}

.newspaper-header {
  text-align: center;
  margin-bottom: 50px;
  position: relative;
}

.cyber-badge {
  background: linear-gradient(45deg, #FF6680, #3355A2);
  color: white;
  padding: 8px 20px;
  font-family: 'Poppins', sans-serif;
  font-weight: bold;
  font-size: 0.9rem;
  border-radius: 20px;
  display: inline-block;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  box-shadow: 0 4px 15px rgba(255, 102, 128, 0.3);
  animation: badgeGlow 2s ease-in-out infinite alternate;
}

@keyframes badgeGlow {
  0% { box-shadow: 0 4px 15px rgba(255, 102, 128, 0.3); }
  100% { box-shadow: 0 4px 25px rgba(255, 102, 128, 0.6); }
}

.newspaper-title {
  font-family: 'Poppins', sans-serif;
  font-size: 3.5rem;
  font-weight: bold;
  color: #ffffff;
  text-shadow:
    0 0 10px rgba(255, 102, 128, 0.5),
    0 0 20px rgba(51, 85, 162, 0.3),
    2px 2px 4px rgba(0,0,0,0.8);
  margin-bottom: 10px;
  letter-spacing: 2px;
  background: linear-gradient(45deg, #FF6680, #3355A2, #FF6680);
  background-size: 200% 200%;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  animation: titleShine 3s ease-in-out infinite;
}

@keyframes titleShine {
  0%, 100% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
}

.newspaper-subtitle {
  font-family: 'Poppins', sans-serif;
  font-size: 1.2rem;
  color: #c0c0c0;
  font-style: italic;
  border-bottom: 3px double #FF6680;
  display: inline-block;
  padding-bottom: 10px;
  margin-bottom: 20px;
}

.clever-quote {
  background: rgba(255, 102, 128, 0.1);
  border: 1px solid rgba(255, 102, 128, 0.3);
  border-radius: 10px;
  padding: 15px 25px;
  margin: 20px auto;
  max-width: 600px;
  font-family: 'Poppins', sans-serif;
  font-style: italic;
  color: #e0e0e0;
  position: relative;
}

.quote-icon {
  font-size: 1.2rem;
  margin-right: 10px;
  color: #FF6680;
}

.quote-text {
  font-size: 1rem;
  line-height: 1.4;
}

.newspaper-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 30px;
  margin-top: 40px;
}

.main-story {
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  border: 2px solid #FF6680;
  border-radius: 15px;
  box-shadow:
    0 0 20px rgba(255, 102, 128, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  padding: 30px;
  position: relative;
  transform: rotate(-0.5deg);
  transition: all 0.3s ease;
  overflow: hidden;
}

.main-story:hover {
  transform: rotate(0deg) scale(1.02);
  box-shadow:
    0 0 30px rgba(255, 102, 128, 0.5),
    0 0 50px rgba(51, 85, 162, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.main-story::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  background: linear-gradient(
    45deg,
    rgba(255, 102, 128, 0.1),
    rgba(51, 85, 162, 0.1),
    rgba(255, 102, 128, 0.1)
  );
  z-index: -1;
  border-radius: 15px;
  animation: borderGlow 3s ease-in-out infinite;
}

@keyframes borderGlow {
  0%, 100% { opacity: 0.5; }
  50% { opacity: 1; }
}

.story-header {
  position: relative;
  margin-bottom: 20px;
}

.breaking-badge {
  background: linear-gradient(45deg, #ff4757, #ff3838);
  color: white;
  padding: 5px 15px;
  font-size: 0.8rem;
  font-weight: bold;
  border-radius: 20px;
  display: inline-block;
  margin-bottom: 15px;
  text-transform: uppercase;
  letter-spacing: 1px;
  animation: breakingPulse 1.5s ease-in-out infinite;
}

@keyframes breakingPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.threat-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin: 15px 0;
}

.threat-tag {
  background: rgba(255, 102, 128, 0.2);
  border: 1px solid rgba(255, 102, 128, 0.5);
  color: #FF6680;
  padding: 4px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
  font-family: 'Poppins', sans-serif;
}

.story-impact {
  background: rgba(51, 85, 162, 0.1);
  border: 1px solid rgba(51, 85, 162, 0.3);
  border-radius: 10px;
  padding: 15px;
  margin: 20px 0;
}

.story-impact h4 {
  color: #3355A2;
  margin-bottom: 10px;
  font-family: 'Poppins', sans-serif;
}

.impact-meter {
  display: flex;
  align-items: center;
  gap: 10px;
}

.impact-level {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: inline-block;
}

.impact-level.high {
  background: #ff4757;
  box-shadow: 0 0 10px rgba(255, 71, 87, 0.5);
}

.impact-level.medium {
  background: #ffa502;
  box-shadow: 0 0 10px rgba(255, 165, 2, 0.5);
}

.impact-level.low {
  background: #2ed573;
  box-shadow: 0 0 10px rgba(46, 213, 115, 0.5);
}

.impact-text {
  color: #e0e0e0;
  font-weight: bold;
  font-size: 0.9rem;
}

.story-headline {
  font-family: 'Poppins', sans-serif;
  font-size: 2.2rem;
  font-weight: bold;
  color: #ffffff;
  line-height: 1.2;
  margin-bottom: 15px;
  border-bottom: 2px solid #FF6680;
  padding-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.story-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px 0;
  border-top: 1px solid rgba(255, 102, 128, 0.3);
  border-bottom: 1px solid rgba(255, 102, 128, 0.3);
}

.story-date {
  font-family: 'Poppins', sans-serif;
  font-size: 0.9rem;
  color: #c0c0c0;
  font-weight: bold;
}

.story-category {
  background: linear-gradient(45deg, #FF6680, #3355A2);
  color: white;
  padding: 8px 15px;
  font-size: 0.8rem;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  border-radius: 20px;
  box-shadow: 0 4px 15px rgba(255, 102, 128, 0.3);
}

.story-summary {
  font-family: 'Poppins', sans-serif;
  font-size: 1.1rem;
  line-height: 1.6;
  color: #e0e0e0;
  margin-bottom: 20px;
  text-align: justify;
}

.story-bullets {
  background: rgba(51, 85, 162, 0.1);
  border-left: 4px solid #3355A2;
  border-radius: 0 10px 10px 0;
  padding: 20px 25px;
  margin: 20px 0;
  backdrop-filter: blur(10px);
}

.story-bullets h4 {
  font-family: 'Poppins', sans-serif;
  color: #3355A2;
  margin-bottom: 15px;
  font-size: 1.1rem;
  font-weight: bold;
}

.story-bullets ul {
  list-style: none;
  padding: 0;
}

.story-bullets li {
  font-family: 'Poppins', sans-serif;
  color: #e0e0e0;
  margin-bottom: 12px;
  padding-left: 25px;
  position: relative;
  line-height: 1.5;
}

.story-bullets li::before {
  content: '🔹';
  position: absolute;
  left: 0;
  color: #3355A2;
  font-size: 1rem;
}

/* Section Headers */
.section-header {
  margin-bottom: 25px;
  text-align: center;
}

.section-title {
  font-family: 'Poppins', sans-serif;
  font-size: 1.5rem;
  font-weight: bold;
  color: #FF6680;
  text-transform: uppercase;
  letter-spacing: 2px;
  margin-bottom: 10px;
  text-shadow: 0 2px 4px rgba(0,0,0,0.5);
}

.section-divider {
  width: 80px;
  height: 3px;
  background: linear-gradient(45deg, #FF6680, #3355A2);
  margin: 0 auto;
  border-radius: 2px;
  box-shadow: 0 2px 10px rgba(255, 102, 128, 0.3);
}

/* Sidebar Stories */
.sidebar-stories {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.sidebar-story {
  background: linear-gradient(135deg, #16213e 0%, #1a1a2e 100%);
  border: 1px solid #3355A2;
  border-radius: 12px;
  box-shadow:
    0 0 15px rgba(51, 85, 162, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  padding: 20px;
  position: relative;
  transform: rotate(0.5deg);
  transition: all 0.3s ease;
  overflow: hidden;
}

.sidebar-story:nth-child(even) {
  transform: rotate(-0.3deg);
}

.sidebar-story:hover {
  transform: rotate(0deg) scale(1.02);
  box-shadow:
    0 0 25px rgba(51, 85, 162, 0.4),
    0 0 40px rgba(255, 102, 128, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.sidebar-story::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(45deg, #3355A2, #FF6680);
  z-index: 1;
}

.sidebar-header {
  margin-bottom: 15px;
}

.alert-badge {
  background: linear-gradient(45deg, #ffa502, #ff6348);
  color: white;
  padding: 3px 10px;
  font-size: 0.7rem;
  font-weight: bold;
  border-radius: 12px;
  display: inline-block;
  margin-bottom: 10px;
  text-transform: uppercase;
  letter-spacing: 1px;
  animation: alertPulse 2s ease-in-out infinite;
}

@keyframes alertPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.sidebar-headline {
  font-family: 'Poppins', sans-serif;
  font-size: 1.1rem;
  font-weight: bold;
  color: #ffffff;
  line-height: 1.3;
  margin-bottom: 0;
  text-shadow: 0 1px 2px rgba(0,0,0,0.5);
}

.sidebar-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 12px 0;
  padding: 8px 0;
  border-top: 1px solid rgba(51, 85, 162, 0.3);
  border-bottom: 1px solid rgba(51, 85, 162, 0.3);
}

.sidebar-date {
  font-family: 'Poppins', sans-serif;
  font-size: 0.75rem;
  color: #c0c0c0;
  font-weight: 500;
}

.priority-level {
  font-family: 'Poppins', sans-serif;
  font-size: 0.7rem;
  font-weight: bold;
  padding: 3px 8px;
  border-radius: 10px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.priority-level.high {
  background: rgba(255, 71, 87, 0.2);
  color: #ff4757;
  border: 1px solid rgba(255, 71, 87, 0.5);
}

.priority-level.medium {
  background: rgba(255, 165, 2, 0.2);
  color: #ffa502;
  border: 1px solid rgba(255, 165, 2, 0.5);
}

.priority-level.low {
  background: rgba(46, 213, 115, 0.2);
  color: #2ed573;
  border: 1px solid rgba(46, 213, 115, 0.5);
}

.sidebar-threat-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 5px;
  margin: 10px 0;
}

.mini-threat-tag {
  background: rgba(255, 102, 128, 0.15);
  border: 1px solid rgba(255, 102, 128, 0.4);
  color: #FF6680;
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 0.7rem;
  font-weight: bold;
  font-family: 'Poppins', sans-serif;
}

.sidebar-summary {
  font-family: 'Poppins', sans-serif;
  font-size: 0.9rem;
  color: #d0d0d0;
  line-height: 1.5;
  margin-bottom: 15px;
}

.sidebar-bullets {
  background: rgba(51, 85, 162, 0.08);
  border-left: 3px solid #3355A2;
  border-radius: 0 8px 8px 0;
  padding: 12px 15px;
  margin: 15px 0;
}

.sidebar-bullets ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar-bullets li {
  font-family: 'Poppins', sans-serif;
  color: #d0d0d0;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
  line-height: 1.4;
  font-size: 0.85rem;
}

.sidebar-bullets li::before {
  content: '🔸';
  position: absolute;
  left: 0;
  color: #3355A2;
  font-size: 0.8rem;
}

.sidebar-read-btn {
  background: linear-gradient(45deg, #3355A2, #1D3670);
  color: white;
  border: none;
  padding: 8px 16px;
  font-family: 'Poppins', sans-serif;
  font-weight: bold;
  font-size: 0.8rem;
  border-radius: 15px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 2px 8px rgba(51, 85, 162, 0.3);
}

.sidebar-read-btn:hover {
  background: linear-gradient(45deg, #1D3670, #3355A2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(51, 85, 162, 0.5);
}

.read-more-btn {
  background: #e74c3c;
  color: white;
  border: none;
  padding: 10px 20px;
  font-family: 'Georgia', serif;
  font-weight: bold;
  text-transform: uppercase;
  letter-spacing: 1px;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 15px;
  border-radius: 3px;
}

.read-more-btn:hover {
  background: #c0392b;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0,0,0,0.2);
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #e74c3c;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.news-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 40px;
  padding: 20px;
  background: rgba(255, 102, 128, 0.05);
  border-radius: 15px;
  border: 1px solid rgba(255, 102, 128, 0.2);
}

.news-refresh-btn {
  background: linear-gradient(45deg, #3355A2, #1D3670);
  color: white;
  border: none;
  padding: 15px 30px;
  font-family: 'Poppins', sans-serif;
  font-weight: bold;
  border-radius: 25px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(51, 85, 162, 0.3);
}

.news-refresh-btn:hover {
  background: linear-gradient(45deg, #1D3670, #3355A2);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(51, 85, 162, 0.5);
}

.refresh-icon {
  margin-right: 8px;
  animation: refreshSpin 2s linear infinite paused;
}

.news-refresh-btn:hover .refresh-icon {
  animation-play-state: running;
}

@keyframes refreshSpin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.threat-level {
  display: flex;
  align-items: center;
  gap: 10px;
  background: rgba(0, 0, 0, 0.3);
  padding: 10px 20px;
  border-radius: 20px;
  border: 1px solid rgba(255, 102, 128, 0.3);
}

.threat-indicator {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  display: inline-block;
  animation: threatPulse 2s ease-in-out infinite;
}

.threat-indicator.high {
  background: #ff4757;
  box-shadow: 0 0 15px rgba(255, 71, 87, 0.7);
}

.threat-indicator.medium {
  background: #ffa502;
  box-shadow: 0 0 15px rgba(255, 165, 2, 0.7);
}

.threat-indicator.low {
  background: #2ed573;
  box-shadow: 0 0 15px rgba(46, 213, 115, 0.7);
}

@keyframes threatPulse {
  0%, 100% { transform: scale(1); opacity: 1; }
  50% { transform: scale(1.2); opacity: 0.8; }
}

.threat-text {
  color: #e0e0e0;
  font-weight: bold;
  font-size: 0.9rem;
  font-family: 'Poppins', sans-serif;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.last-update {
  font-family: 'Poppins', sans-serif;
  font-size: 0.9rem;
  color: #c0c0c0;
  margin-top: 15px;
  text-align: center;
  font-style: italic;
}

.loading-state, .error-state, .no-news-state {
  text-align: center;
  padding: 60px 20px;
  font-family: 'Georgia', serif;
}

.loading-state p, .error-state p, .no-news-state p {
  font-size: 1.2rem;
  color: #7f8c8d;
  margin: 20px 0;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
  .newspaper-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .newspaper-title {
    font-size: 2.5rem;
  }

  .main-story {
    transform: rotate(0deg);
    padding: 20px;
  }

  .story-headline {
    font-size: 1.8rem;
  }

  .sidebar-story {
    transform: rotate(0deg);
  }

  .story-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .news-actions {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .threat-tags {
    justify-content: center;
  }

  .clever-quote {
    margin: 15px 10px;
    padding: 12px 20px;
  }

  .cyber-badge {
    font-size: 0.8rem;
    padding: 6px 15px;
  }

  .section-title {
    font-size: 1.2rem;
    letter-spacing: 1px;
  }

  .sidebar-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .sidebar-threat-tags {
    justify-content: flex-start;
  }
}

@media (max-width: 480px) {
  .newspaper-title {
    font-size: 2rem;
    letter-spacing: 1px;
  }

  .story-headline {
    font-size: 1.5rem;
  }

  .main-story, .sidebar-story {
    padding: 15px;
  }

  .cyber-news-section {
    padding: 40px 15px;
  }
}

/* Responsive Design for Newspaper */
@media (max-width: 768px) {
  .newspaper-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .newspaper-title {
    font-size: 2.5rem;
  }

  .main-story {
    transform: rotate(0deg);
    padding: 20px;
  }

  .story-headline {
    font-size: 1.8rem;
  }

  .sidebar-story {
    transform: rotate(0deg);
  }
}