/**
 * News Integration for CybeReach Website
 * Handles fetching and displaying cybersecurity news with newspaper design
 */

class NewsManager {
    constructor() {
        this.apiBaseUrl = 'http://localhost:5000/api';
        this.newsData = [];
        this.isLoading = false;
        this.lastUpdate = null;
        
        // Initialize when DOM is ready
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
        } else {
            this.init();
        }
    }

    async init() {
        console.log('🚀 Initializing News Manager...');
        await this.loadNews();
        this.setupEventListeners();
        this.startAutoRefresh();
    }

    setupEventListeners() {
        // Refresh button
        const refreshBtn = document.getElementById('news-refresh-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.refreshNews());
        }

        // Search functionality
        const searchInput = document.getElementById('news-search');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.searchNews(e.target.value));
        }
    }

    async loadNews() {
        if (this.isLoading) return;
        
        this.isLoading = true;
        this.showLoadingState();

        try {
            console.log('📰 Fetching news from API...');
            const response = await fetch(`${this.apiBaseUrl}/news?limit=8`);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.success && data.articles) {
                this.newsData = data.articles;
                this.lastUpdate = data.last_update;
                this.displayNews();
                console.log(`✅ Loaded ${this.newsData.length} articles`);
            } else {
                throw new Error('Invalid response format');
            }
        } catch (error) {
            console.error('❌ Error loading news:', error);
            this.showErrorState();
            // Load demo data as fallback
            this.loadDemoData();
        } finally {
            this.isLoading = false;
        }
    }

    loadDemoData() {
        console.log('📋 Loading demo news data...');
        this.newsData = [
            {
                title: "Major Cybersecurity Breach Affects Millions",
                date: "2024-05-26",
                summary: "A significant data breach has been discovered affecting multiple organizations worldwide.",
                bullet_summary: "• Data breach affects over 10 million users\n• Investigation ongoing by cybersecurity experts\n• Companies urged to update security protocols",
                url: "#"
            },
            {
                title: "New AI-Powered Security Tool Launched",
                date: "2024-05-25",
                summary: "Revolutionary AI technology promises to detect threats 10x faster than traditional methods.",
                bullet_summary: "• AI detects threats in real-time\n• 99.9% accuracy rate in testing\n• Available for enterprise customers",
                url: "#"
            },
            {
                title: "Ransomware Attacks Increase by 40%",
                date: "2024-05-24",
                summary: "Security researchers report a significant increase in ransomware attacks targeting small businesses.",
                bullet_summary: "• 40% increase in ransomware attacks\n• Small businesses primary targets\n• New prevention strategies recommended",
                url: "#"
            }
        ];
        this.displayNews();
    }

    displayNews() {
        const newsContainer = document.getElementById('cyber-news-container');
        if (!newsContainer) {
            console.warn('News container not found');
            return;
        }

        if (!this.newsData || this.newsData.length === 0) {
            newsContainer.innerHTML = this.getNoNewsHTML();
            return;
        }

        const mainStory = this.newsData[0];
        const sidebarStories = this.newsData.slice(1, 4);

        newsContainer.innerHTML = `
            <div class="newspaper-container">
                <div class="newspaper-header">
                    <h1 class="newspaper-title">CYBER SECURITY TIMES</h1>
                    <p class="newspaper-subtitle">Latest Cybersecurity News & Analysis</p>
                    ${this.lastUpdate ? `<p class="last-update">Last updated: ${this.formatDate(this.lastUpdate)}</p>` : ''}
                </div>
                
                <div class="newspaper-grid">
                    <div class="main-story">
                        ${this.renderMainStory(mainStory)}
                    </div>
                    
                    <div class="sidebar-stories">
                        ${sidebarStories.map(story => this.renderSidebarStory(story)).join('')}
                    </div>
                </div>
                
                <button id="news-refresh-btn" class="news-refresh-btn">
                    <span class="refresh-icon">🔄</span> Refresh News
                </button>
            </div>
        `;

        // Re-attach event listeners
        this.setupEventListeners();
    }

    renderMainStory(story) {
        const bullets = this.formatBulletPoints(story.bullet_summary || story.summary);
        
        return `
            <h2 class="story-headline">${this.escapeHtml(story.title)}</h2>
            <div class="story-meta">
                <span class="story-date">${this.formatDate(story.date)}</span>
                <span class="story-category">CYBERSECURITY</span>
            </div>
            <p class="story-summary">${this.escapeHtml(story.summary || '')}</p>
            <div class="story-bullets">
                <h4>Key Points:</h4>
                <ul>
                    ${bullets.map(bullet => `<li>${this.escapeHtml(bullet)}</li>`).join('')}
                </ul>
            </div>
            ${story.url && story.url !== '#' ? `<button class="read-more-btn" onclick="window.open('${story.url}', '_blank')">Read Full Article</button>` : ''}
        `;
    }

    renderSidebarStory(story) {
        return `
            <div class="sidebar-story">
                <h3 class="sidebar-headline">${this.escapeHtml(story.title)}</h3>
                <p class="sidebar-summary">${this.escapeHtml(this.truncateText(story.summary || '', 120))}</p>
                <p class="sidebar-date">${this.formatDate(story.date)}</p>
                ${story.url && story.url !== '#' ? `<button class="read-more-btn" onclick="window.open('${story.url}', '_blank')">Read More</button>` : ''}
            </div>
        `;
    }

    formatBulletPoints(text) {
        if (!text) return ['No additional details available'];
        
        // Split by bullet points or newlines
        const bullets = text.split(/[•\n]/).filter(item => item.trim().length > 0);
        
        if (bullets.length === 0) {
            // If no bullets found, create some from the text
            const sentences = text.split('.').filter(s => s.trim().length > 10);
            return sentences.slice(0, 3).map(s => s.trim());
        }
        
        return bullets.slice(0, 4).map(bullet => bullet.trim());
    }

    formatDate(dateString) {
        if (!dateString) return 'Unknown date';
        
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
            });
        } catch (error) {
            return dateString;
        }
    }

    truncateText(text, maxLength) {
        if (!text || text.length <= maxLength) return text;
        return text.substring(0, maxLength).trim() + '...';
    }

    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    showLoadingState() {
        const newsContainer = document.getElementById('cyber-news-container');
        if (newsContainer) {
            newsContainer.innerHTML = `
                <div class="newspaper-container">
                    <div class="newspaper-header">
                        <h1 class="newspaper-title">CYBER SECURITY TIMES</h1>
                        <p class="newspaper-subtitle">Loading latest news...</p>
                    </div>
                    <div class="loading-state">
                        <div class="loading-spinner"></div>
                        <p>Fetching the latest cybersecurity news...</p>
                    </div>
                </div>
            `;
        }
    }

    showErrorState() {
        const newsContainer = document.getElementById('cyber-news-container');
        if (newsContainer) {
            newsContainer.innerHTML = `
                <div class="newspaper-container">
                    <div class="newspaper-header">
                        <h1 class="newspaper-title">CYBER SECURITY TIMES</h1>
                        <p class="newspaper-subtitle">Unable to load news</p>
                    </div>
                    <div class="error-state">
                        <p>⚠️ Unable to fetch the latest news. Please try again later.</p>
                        <button onclick="newsManager.loadNews()" class="news-refresh-btn">Try Again</button>
                    </div>
                </div>
            `;
        }
    }

    getNoNewsHTML() {
        return `
            <div class="newspaper-container">
                <div class="newspaper-header">
                    <h1 class="newspaper-title">CYBER SECURITY TIMES</h1>
                    <p class="newspaper-subtitle">No news available</p>
                </div>
                <div class="no-news-state">
                    <p>📰 No cybersecurity news available at the moment.</p>
                    <button onclick="newsManager.refreshNews()" class="news-refresh-btn">Refresh News</button>
                </div>
            </div>
        `;
    }

    async refreshNews() {
        console.log('🔄 Refreshing news...');
        
        try {
            // Trigger backend refresh
            await fetch(`${this.apiBaseUrl}/news/refresh`, { method: 'POST' });
            
            // Wait a moment for the refresh to process
            setTimeout(() => {
                this.loadNews();
            }, 2000);
            
        } catch (error) {
            console.error('❌ Error refreshing news:', error);
            // Still try to reload current data
            this.loadNews();
        }
    }

    async searchNews(query) {
        if (!query || query.length < 2) {
            this.displayNews();
            return;
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/news/search?q=${encodeURIComponent(query)}`);
            const data = await response.json();
            
            if (data.success) {
                this.newsData = data.articles;
                this.displayNews();
            }
        } catch (error) {
            console.error('❌ Error searching news:', error);
        }
    }

    startAutoRefresh() {
        // Refresh news every 30 minutes
        setInterval(() => {
            console.log('🔄 Auto-refreshing news...');
            this.loadNews();
        }, 30 * 60 * 1000);
    }
}

// Initialize the news manager
const newsManager = new NewsManager();
